{"name": "dormitory-management-system", "version": "1.0.0", "description": "宿舍管理系统 - 可视化管理88个宿舍的Web应用", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["dormitory", "management", "system", "宿舍管理"], "author": "Admin", "license": "MIT"}