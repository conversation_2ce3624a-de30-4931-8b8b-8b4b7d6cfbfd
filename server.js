const express = require('express');
const session = require('express-session');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const bcrypt = require('bcryptjs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(session({
    secret: 'dormitory-management-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24小时
}));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 数据存储 (内存存储，生产环境建议使用数据库)
let users = [
    {
        id: 1,
        username: 'admin',
        password: bcrypt.hashSync('admin123', 10),
        role: 'admin',
        permissions: 'all'
    }
];

let rooms = [];
let maintenanceRecords = [];

// 初始化房间数据
function initializeRooms() {
    const floors = [
        { floor: 3, start: 301, end: 325 },
        { floor: 4, start: 401, end: 421 },
        { floor: 5, start: 501, end: 521 },
        { floor: 6, start: 601, end: 621 }
    ];

    floors.forEach(floorInfo => {
        for (let roomNum = floorInfo.start; roomNum <= floorInfo.end; roomNum++) {
            rooms.push({
                id: roomNum,
                roomNumber: roomNum.toString(),
                floor: floorInfo.floor,
                status: 'empty', // empty, occupied, warning, overdue, self-use
                tenant: null,
                tenantUnit: '',
                tenantPhone: '',
                area: 20, // 默认面积20平米
                leaseStartDate: null,
                leaseEndDate: null,
                financialStatus: 'none', // none, paid, invoice-pending, invoice-issued
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
    });

    // 添加一些示例数据用于测试
    addSampleData();
}

// 添加示例数据
function addSampleData() {
    const now = new Date();
    const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
    const warningDate = new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000); // 10天后
    const overdueDate = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000); // 5天前

    // 正常入住的房间
    updateRoom(301, {
        tenant: '张三',
        tenantUnit: '技术部',
        tenantPhone: '13800138001',
        leaseStartDate: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000),
        leaseEndDate: futureDate,
        financialStatus: 'paid'
    });

    // 租期临近的房间
    updateRoom(302, {
        tenant: '李四',
        tenantUnit: '市场部',
        tenantPhone: '13800138002',
        leaseStartDate: new Date(now.getTime() - 80 * 24 * 60 * 60 * 1000),
        leaseEndDate: warningDate,
        financialStatus: 'invoice-pending'
    });

    // 超期的房间
    updateRoom(303, {
        tenant: '王五',
        tenantUnit: '财务部',
        tenantPhone: '13800138003',
        leaseStartDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
        leaseEndDate: overdueDate,
        financialStatus: 'none'
    });

    // 自用房间
    updateRoom(401, {
        status: 'self-use',
        tenant: '公司自用',
        tenantUnit: '行政部',
        tenantPhone: '13800138004',
        financialStatus: 'none'
    });

    // 添加一个子账号用于测试
    users.push({
        id: 2,
        username: 'subuser',
        password: bcrypt.hashSync('sub123', 10),
        role: 'sub',
        permissions: ['301', '302', '303', '401', '501', '601'] // 可以管理这些房间
    });

    // 添加一些维修记录
    maintenanceRecords.push(
        {
            id: 1,
            roomId: 301,
            description: '水龙头漏水',
            reportedBy: 'admin',
            reportedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
            status: 'completed',
            completedAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000),
            notes: '已更换水龙头'
        },
        {
            id: 2,
            roomId: 302,
            description: '空调不制冷',
            reportedBy: 'subuser',
            reportedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
            status: 'pending',
            completedAt: null,
            notes: '等待维修师傅上门'
        }
    );
}

// 更新房间信息的辅助函数
function updateRoom(roomId, updates) {
    const room = rooms.find(r => r.id === roomId);
    if (room) {
        Object.assign(room, updates);
        room.updatedAt = new Date();
    }
}

// 权限验证中间件
function requireAuth(req, res, next) {
    if (!req.session.user) {
        return res.status(401).json({ error: '需要登录' });
    }
    next();
}

function requireAdmin(req, res, next) {
    if (!req.session.user || req.session.user.role !== 'admin') {
        return res.status(403).json({ error: '需要管理员权限' });
    }
    next();
}

// 检查房间权限
function checkRoomPermission(req, res, next) {
    const user = req.session.user;
    const roomId = req.params.roomId || req.body.roomId;

    if (user.role === 'admin') {
        return next();
    }

    if (user.permissions && user.permissions.includes(roomId)) {
        return next();
    }

    return res.status(403).json({ error: '没有该房间的操作权限' });
}

// 工具函数
function updateRoomStatus(room) {
    if (!room.leaseEndDate) {
        room.status = 'empty';
        return;
    }

    const now = new Date();
    const leaseEnd = new Date(room.leaseEndDate);
    const daysUntilExpiry = Math.ceil((leaseEnd - now) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) {
        room.status = 'overdue'; // 超期
    } else if (daysUntilExpiry <= 15) {
        room.status = 'warning'; // 临近到期
    } else if (room.tenant) {
        room.status = 'occupied'; // 正常入住
    } else {
        room.status = 'empty'; // 空房
    }
}

// API 路由

// 认证相关API
app.post('/api/auth/login', async (req, res) => {
    const { username, password } = req.body;

    const user = users.find(u => u.username === username);
    if (!user || !bcrypt.compareSync(password, user.password)) {
        return res.status(401).json({ error: '用户名或密码错误' });
    }

    req.session.user = {
        id: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions
    };

    res.json({
        message: '登录成功',
        user: req.session.user
    });
});

app.post('/api/auth/logout', (req, res) => {
    req.session.destroy();
    res.json({ message: '退出成功' });
});

app.get('/api/auth/status', (req, res) => {
    if (req.session.user) {
        res.json({ user: req.session.user });
    } else {
        res.status(401).json({ error: '未登录' });
    }
});

// 房间相关API
app.get('/api/rooms', (req, res) => {
    // 更新所有房间状态
    rooms.forEach(updateRoomStatus);

    if (!req.session.user) {
        // 未登录用户只能看到房间号和状态
        const publicRooms = rooms.map(room => ({
            id: room.id,
            roomNumber: room.roomNumber,
            floor: room.floor,
            status: room.status
        }));
        return res.json(publicRooms);
    }

    const user = req.session.user;
    if (user.role === 'admin') {
        // 管理员可以看到所有房间的完整信息
        return res.json(rooms);
    }

    // 子账号只能看到有权限的房间
    const allowedRooms = rooms.filter(room =>
        user.permissions === 'all' ||
        (Array.isArray(user.permissions) && user.permissions.includes(room.id.toString()))
    );

    res.json(allowedRooms);
});

app.get('/api/rooms/:roomId', requireAuth, checkRoomPermission, (req, res) => {
    const roomId = parseInt(req.params.roomId);
    const room = rooms.find(r => r.id === roomId);

    if (!room) {
        return res.status(404).json({ error: '房间不存在' });
    }

    updateRoomStatus(room);
    res.json(room);
});

// 用户管理API (仅管理员可用)
app.get('/api/users', requireAdmin, (req, res) => {
    const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        createdAt: user.createdAt || new Date()
    }));
    res.json(safeUsers);
});

app.post('/api/users', requireAdmin, (req, res) => {
    const { username, password, role, permissions } = req.body;

    if (!username || !password) {
        return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    if (users.find(u => u.username === username)) {
        return res.status(400).json({ error: '用户名已存在' });
    }

    const newUser = {
        id: users.length + 1,
        username,
        password: bcrypt.hashSync(password, 10),
        role: role || 'sub',
        permissions: permissions || [],
        createdAt: new Date()
    };

    users.push(newUser);

    res.json({
        message: '用户创建成功',
        user: {
            id: newUser.id,
            username: newUser.username,
            role: newUser.role,
            permissions: newUser.permissions
        }
    });
});

app.put('/api/users/:userId', requireAdmin, (req, res) => {
    const userId = parseInt(req.params.userId);
    const user = users.find(u => u.id === userId);

    if (!user) {
        return res.status(404).json({ error: '用户不存在' });
    }

    const { username, password, role, permissions } = req.body;

    if (username && username !== user.username) {
        if (users.find(u => u.username === username && u.id !== userId)) {
            return res.status(400).json({ error: '用户名已存在' });
        }
        user.username = username;
    }

    if (password) {
        user.password = bcrypt.hashSync(password, 10);
    }

    if (role) {
        user.role = role;
    }

    if (permissions !== undefined) {
        user.permissions = permissions;
    }

    user.updatedAt = new Date();

    res.json({
        message: '用户更新成功',
        user: {
            id: user.id,
            username: user.username,
            role: user.role,
            permissions: user.permissions
        }
    });
});

app.delete('/api/users/:userId', requireAdmin, (req, res) => {
    const userId = parseInt(req.params.userId);
    const userIndex = users.findIndex(u => u.id === userId);

    if (userIndex === -1) {
        return res.status(404).json({ error: '用户不存在' });
    }

    if (users[userIndex].id === 1) {
        return res.status(400).json({ error: '不能删除默认管理员账号' });
    }

    users.splice(userIndex, 1);
    res.json({ message: '用户删除成功' });
});

// 房间管理API
app.put('/api/rooms/:roomId', requireAuth, checkRoomPermission, (req, res) => {
    const roomId = parseInt(req.params.roomId);
    const room = rooms.find(r => r.id === roomId);

    if (!room) {
        return res.status(404).json({ error: '房间不存在' });
    }

    const user = req.session.user;
    const {
        tenant,
        tenantUnit,
        tenantPhone,
        area,
        leaseStartDate,
        leaseEndDate,
        financialStatus,
        status
    } = req.body;

    // 子账号不能修改面积
    if (user.role !== 'admin' && area !== undefined) {
        return res.status(403).json({ error: '子账号不能修改房间面积' });
    }

    // 更新房间信息
    if (tenant !== undefined) room.tenant = tenant;
    if (tenantUnit !== undefined) room.tenantUnit = tenantUnit;
    if (tenantPhone !== undefined) room.tenantPhone = tenantPhone;
    if (area !== undefined && user.role === 'admin') room.area = area;
    if (leaseStartDate !== undefined) room.leaseStartDate = leaseStartDate;
    if (leaseEndDate !== undefined) room.leaseEndDate = leaseEndDate;
    if (financialStatus !== undefined) room.financialStatus = financialStatus;
    if (status !== undefined) room.status = status;

    room.updatedAt = new Date();

    // 更新房间状态
    updateRoomStatus(room);

    res.json({
        message: '房间信息更新成功',
        room: room
    });
});

// 一键续住功能
app.post('/api/rooms/:roomId/renew', requireAuth, checkRoomPermission, (req, res) => {
    const roomId = parseInt(req.params.roomId);
    const room = rooms.find(r => r.id === roomId);

    if (!room) {
        return res.status(404).json({ error: '房间不存在' });
    }

    const { months = 12 } = req.body; // 默认续租12个月

    if (!room.leaseEndDate) {
        return res.status(400).json({ error: '房间没有租期信息，无法续租' });
    }

    const currentEndDate = new Date(room.leaseEndDate);
    const newEndDate = new Date(currentEndDate);
    newEndDate.setMonth(newEndDate.getMonth() + months);

    room.leaseEndDate = newEndDate;
    room.financialStatus = 'paid';
    room.updatedAt = new Date();

    // 更新房间状态
    updateRoomStatus(room);

    res.json({
        message: `房间 ${room.roomNumber} 续租成功，新的到期日期: ${newEndDate.toLocaleDateString()}`,
        room: room
    });
});

// 获取维修记录
app.get('/api/rooms/:roomId/maintenance', requireAuth, checkRoomPermission, (req, res) => {
    const roomId = parseInt(req.params.roomId);
    const records = maintenanceRecords.filter(r => r.roomId === roomId);
    res.json(records);
});

// 添加维修记录
app.post('/api/rooms/:roomId/maintenance', requireAuth, checkRoomPermission, (req, res) => {
    const roomId = parseInt(req.params.roomId);
    const { description, notes } = req.body;

    if (!description) {
        return res.status(400).json({ error: '维修描述不能为空' });
    }

    const newRecord = {
        id: maintenanceRecords.length + 1,
        roomId: roomId,
        description: description,
        reportedBy: req.session.user.username,
        reportedAt: new Date(),
        status: 'pending',
        completedAt: null,
        notes: notes || ''
    };

    maintenanceRecords.push(newRecord);

    res.json({
        message: '维修记录添加成功',
        record: newRecord
    });
});

// 更新维修记录状态
app.put('/api/maintenance/:recordId', requireAuth, (req, res) => {
    const recordId = parseInt(req.params.recordId);
    const record = maintenanceRecords.find(r => r.id === recordId);

    if (!record) {
        return res.status(404).json({ error: '维修记录不存在' });
    }

    // 检查权限
    const user = req.session.user;
    if (user.role !== 'admin' &&
        !(user.permissions && user.permissions.includes(record.roomId.toString()))) {
        return res.status(403).json({ error: '没有权限修改此维修记录' });
    }

    const { status, notes } = req.body;

    if (status) {
        record.status = status;
        if (status === 'completed') {
            record.completedAt = new Date();
        }
    }

    if (notes !== undefined) {
        record.notes = notes;
    }

    record.updatedAt = new Date();

    res.json({
        message: '维修记录更新成功',
        record: record
    });
});

// 获取所有维修记录 (仅管理员)
app.get('/api/maintenance', requireAdmin, (req, res) => {
    res.json(maintenanceRecords);
});

// 初始化数据
initializeRooms();

console.log(`宿舍管理系统服务器启动在端口 ${PORT}`);
console.log(`访问 http://localhost:${PORT} 查看系统`);
console.log('默认管理员账号: admin, 密码: admin123');

app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});