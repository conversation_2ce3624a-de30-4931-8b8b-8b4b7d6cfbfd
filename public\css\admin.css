/* 管理员面板样式 */
.admin-tabs {
    display: flex;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: white;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-btn.active {
    background-color: #3498db;
    color: white;
    border-bottom-color: #2980b9;
}

.tab-content {
    display: none;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    color: #2c3e50;
    margin: 0;
}

.filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* 表格样式 */
.users-table, .rooms-table, .maintenance-table {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

table tr:hover {
    background-color: #f8f9fa;
}

.status-cell {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.status-cell.empty { background-color: #ffffff; border: 1px solid #ddd; }
.status-cell.occupied { background-color: #27ae60; color: white; }
.status-cell.warning { background-color: #f1c40f; }
.status-cell.overdue { background-color: #e74c3c; color: white; }
.status-cell.self-use { background-color: #3498db; color: white; }
.status-cell.pending { background-color: #f39c12; color: white; }
.status-cell.completed { background-color: #27ae60; color: white; }

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-edit {
    background-color: #3498db;
    color: white;
}

.btn-edit:hover {
    background-color: #2980b9;
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
}

.btn-delete:hover {
    background-color: #c0392b;
}

.btn-complete {
    background-color: #27ae60;
    color: white;
}

.btn-complete:hover {
    background-color: #229954;
}

/* 权限网格 */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.permission-item input[type="checkbox"] {
    margin: 0;
}

.permission-item label {
    margin: 0;
    font-size: 12px;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-tabs {
        flex-direction: column;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filters {
        justify-content: stretch;
    }
    
    .filters select {
        flex: 1;
    }
    
    table {
        font-size: 12px;
    }
    
    table th,
    table td {
        padding: 8px 4px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .permissions-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }
}
