/* 管理员面板样式 */
.admin-tabs {
    display: flex;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: white;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-btn.active {
    background-color: #3498db;
    color: white;
    border-bottom-color: #2980b9;
}

.tab-content {
    display: none;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    color: #2c3e50;
    margin: 0;
}

.filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* 表格样式 */
.users-table, .rooms-table, .maintenance-table {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

table tr:hover {
    background-color: #f8f9fa;
}

.status-cell {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.status-cell.empty { background-color: #ffffff; border: 1px solid #ddd; }
.status-cell.occupied { background-color: #27ae60; color: white; }
.status-cell.warning { background-color: #f1c40f; }
.status-cell.overdue { background-color: #e74c3c; color: white; }
.status-cell.self-use { background-color: #3498db; color: white; }
.status-cell.pending { background-color: #f39c12; color: white; }
.status-cell.completed { background-color: #27ae60; color: white; }

/* 紧急程度样式 */
.urgency-low { background-color: #95a5a6; color: white; }
.urgency-medium { background-color: #f39c12; color: white; }
.urgency-high { background-color: #e74c3c; color: white; }

/* 报修类型样式 */
.repair-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 5px;
}

.repair-type.water { background-color: #3498db; color: white; }
.repair-type.electricity { background-color: #f1c40f; }
.repair-type.lighting { background-color: #9b59b6; color: white; }
.repair-type.network { background-color: #1abc9c; color: white; }
.repair-type.furniture { background-color: #8b4513; color: white; }
.repair-type.appliance { background-color: #34495e; color: white; }
.repair-type.other { background-color: #95a5a6; color: white; }

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-edit {
    background-color: #3498db;
    color: white;
}

.btn-edit:hover {
    background-color: #2980b9;
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
}

.btn-delete:hover {
    background-color: #c0392b;
}

.btn-complete {
    background-color: #27ae60;
    color: white;
}

.btn-complete:hover {
    background-color: #229954;
}

/* 权限网格 */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.permission-item input[type="checkbox"] {
    margin: 0;
}

.permission-item label {
    margin: 0;
    font-size: 12px;
    cursor: pointer;
}

/* 维修详情模态框 */
.maintenance-detail-modal {
    width: 700px;
    max-height: 80vh;
    overflow-y: auto;
}

.maintenance-detail h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: bold;
    color: #2c3e50;
    font-size: 14px;
}

.detail-item span {
    color: #555;
}

.description-box {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    white-space: pre-wrap;
    word-wrap: break-word;
    min-height: 60px;
}

.detail-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-tabs {
        flex-direction: column;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filters {
        justify-content: stretch;
    }

    .filters select {
        flex: 1;
    }

    table {
        font-size: 12px;
    }

    table th,
    table td {
        padding: 8px 4px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .permissions-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }

    .maintenance-detail-modal {
        width: 95%;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .detail-actions {
        flex-direction: column;
    }
}
