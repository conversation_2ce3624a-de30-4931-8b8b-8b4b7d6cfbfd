<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 宿舍管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/admin.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>管理员面板</h1>
            <div class="user-info">
                <span id="userInfo"></span>
                <a href="/" class="btn btn-secondary">返回首页</a>
                <button id="logoutBtn" class="btn btn-secondary">退出</button>
            </div>
        </header>

        <div class="admin-tabs">
            <button class="tab-btn active" onclick="showTab('users')">用户管理</button>
            <button class="tab-btn" onclick="showTab('rooms')">房间管理</button>
            <button class="tab-btn" onclick="showTab('maintenance')">维修管理</button>
        </div>

        <!-- 用户管理标签页 -->
        <div id="users-tab" class="tab-content active">
            <div class="section-header">
                <h2>用户管理</h2>
                <button class="btn btn-primary" onclick="showCreateUserModal()">创建新用户</button>
            </div>
            
            <div class="users-table">
                <table id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>角色</th>
                            <th>权限</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 房间管理标签页 -->
        <div id="rooms-tab" class="tab-content">
            <div class="section-header">
                <h2>房间管理</h2>
                <div class="filters">
                    <select id="statusFilter" onchange="filterRooms()">
                        <option value="">所有状态</option>
                        <option value="empty">空房</option>
                        <option value="occupied">正常入住</option>
                        <option value="warning">租期临近</option>
                        <option value="overdue">超期未缴费</option>
                        <option value="self-use">自用房间</option>
                    </select>
                    <select id="floorFilter" onchange="filterRooms()">
                        <option value="">所有楼层</option>
                        <option value="3">3楼</option>
                        <option value="4">4楼</option>
                        <option value="5">5楼</option>
                        <option value="6">6楼</option>
                    </select>
                </div>
            </div>
            
            <div class="rooms-table">
                <table id="roomsTable">
                    <thead>
                        <tr>
                            <th>房间号</th>
                            <th>楼层</th>
                            <th>状态</th>
                            <th>入住人</th>
                            <th>单位</th>
                            <th>电话</th>
                            <th>租期结束</th>
                            <th>财务状况</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="roomsTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 维修管理标签页 -->
        <div id="maintenance-tab" class="tab-content">
            <div class="section-header">
                <h2>维修管理</h2>
                <div class="filters">
                    <select id="maintenanceStatusFilter" onchange="filterMaintenance()">
                        <option value="">所有状态</option>
                        <option value="pending">待处理</option>
                        <option value="completed">已完成</option>
                    </select>
                    <select id="urgencyFilter" onchange="filterMaintenance()">
                        <option value="">所有紧急程度</option>
                        <option value="high">紧急</option>
                        <option value="medium">较急</option>
                        <option value="low">一般</option>
                    </select>
                    <select id="typeFilter" onchange="filterMaintenance()">
                        <option value="">所有类型</option>
                        <option value="water">水路问题</option>
                        <option value="electricity">电路问题</option>
                        <option value="lighting">照明问题</option>
                        <option value="network">网络问题</option>
                        <option value="furniture">家具问题</option>
                        <option value="appliance">电器问题</option>
                    </select>
                </div>
            </div>
            
            <div class="maintenance-table">
                <table id="maintenanceTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>房间号</th>
                            <th>类型</th>
                            <th>紧急程度</th>
                            <th>描述</th>
                            <th>报告人</th>
                            <th>联系电话</th>
                            <th>报告时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="maintenanceTableBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 创建用户模态框 -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>创建新用户</h2>
            <form id="createUserForm">
                <div class="form-group">
                    <label for="newUsername">用户名:</label>
                    <input type="text" id="newUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">密码:</label>
                    <input type="password" id="newPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="newRole">角色:</label>
                    <select id="newRole" name="role" onchange="togglePermissions()">
                        <option value="sub">子账号</option>
                        <option value="admin">管理员</option>
                    </select>
                </div>
                <div class="form-group" id="permissionsGroup">
                    <label>房间权限:</label>
                    <div class="permissions-grid" id="permissionsGrid">
                        <!-- 权限复选框将通过JavaScript生成 -->
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">创建用户</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createUserModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/js/admin.js"></script>
</body>
</html>
