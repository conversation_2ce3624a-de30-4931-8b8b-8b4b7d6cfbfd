// 全局变量
let currentUser = null;
let rooms = [];

// DOM 元素
const loginBtn = document.getElementById('loginBtn');
const logoutBtn = document.getElementById('logoutBtn');
const adminLink = document.getElementById('adminLink');
const userInfo = document.getElementById('userInfo');
const loginModal = document.getElementById('loginModal');
const roomModal = document.getElementById('roomModal');
const loginForm = document.getElementById('loginForm');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    loadRooms();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    loginBtn.addEventListener('click', showLoginModal);
    logoutBtn.addEventListener('click', logout);
    loginForm.addEventListener('submit', handleLogin);
    
    // 模态框关闭事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
}

// 检查认证状态
async function checkAuthStatus() {
    try {
        const response = await fetch('/api/auth/status');
        if (response.ok) {
            const data = await response.json();
            currentUser = data.user;
            updateUI();
        }
    } catch (error) {
        console.error('检查认证状态失败:', error);
    }
}

// 更新UI
function updateUI() {
    if (currentUser) {
        loginBtn.style.display = 'none';
        logoutBtn.style.display = 'inline-block';
        adminLink.style.display = currentUser.role === 'admin' ? 'inline-block' : 'none';
        userInfo.textContent = `欢迎, ${currentUser.username} (${currentUser.role === 'admin' ? '管理员' : '子账号'})`;
    } else {
        loginBtn.style.display = 'inline-block';
        logoutBtn.style.display = 'none';
        adminLink.style.display = 'none';
        userInfo.textContent = '';
    }
}

// 显示登录模态框
function showLoginModal() {
    loginModal.style.display = 'block';
}

// 处理登录
async function handleLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(loginForm);
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentUser = data.user;
            updateUI();
            loginModal.style.display = 'none';
            loginForm.reset();
            loadRooms(); // 重新加载房间数据
            alert('登录成功!');
        } else {
            alert(data.error || '登录失败');
        }
    } catch (error) {
        console.error('登录失败:', error);
        alert('登录失败，请稍后重试');
    }
}

// 退出登录
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST'
        });
        
        if (response.ok) {
            currentUser = null;
            updateUI();
            loadRooms(); // 重新加载房间数据
            alert('已退出登录');
        }
    } catch (error) {
        console.error('退出登录失败:', error);
    }
}

// 加载房间数据
async function loadRooms() {
    try {
        const response = await fetch('/api/rooms');
        if (response.ok) {
            rooms = await response.json();
            renderRooms();
        }
    } catch (error) {
        console.error('加载房间数据失败:', error);
    }
}

// 渲染房间
function renderRooms() {
    const floors = [
        { floor: 6, start: 601, end: 621, containerId: 'floor6' },
        { floor: 5, start: 501, end: 521, containerId: 'floor5' },
        { floor: 4, start: 401, end: 421, containerId: 'floor4' },
        { floor: 3, start: 301, end: 325, containerId: 'floor3' }
    ];
    
    floors.forEach(floorInfo => {
        const container = document.getElementById(floorInfo.containerId);
        container.innerHTML = '';
        
        for (let roomNum = floorInfo.start; roomNum <= floorInfo.end; roomNum++) {
            const room = rooms.find(r => r.id === roomNum);
            if (room) {
                const roomElement = createRoomElement(room);
                container.appendChild(roomElement);
            }
        }
    });
}

// 创建房间元素
function createRoomElement(room) {
    const roomDiv = document.createElement('div');
    roomDiv.className = `room ${room.status}`;
    roomDiv.dataset.roomId = room.id;
    
    const roomNumber = document.createElement('div');
    roomNumber.className = 'room-number';
    roomNumber.textContent = room.roomNumber;
    
    const roomInfo = document.createElement('div');
    roomInfo.className = 'room-info';
    
    // 根据登录状态显示不同信息
    if (currentUser) {
        // 检查用户权限
        const hasPermission = currentUser.role === 'admin' || 
                            (currentUser.permissions && currentUser.permissions.includes(room.id.toString()));
        
        if (hasPermission) {
            if (room.tenant) {
                roomInfo.innerHTML = `${room.tenantUnit}<br>${room.tenant}`;
            } else {
                roomInfo.textContent = '空房';
            }
        } else {
            roomInfo.textContent = '无权限';
        }
    }
    
    roomDiv.appendChild(roomNumber);
    roomDiv.appendChild(roomInfo);
    
    // 添加点击事件
    roomDiv.addEventListener('click', () => showRoomDetails(room));
    
    return roomDiv;
}

// 显示房间详情
function showRoomDetails(room) {
    if (!currentUser) {
        alert('请先登录查看房间详情');
        return;
    }
    
    // 检查权限
    const hasPermission = currentUser.role === 'admin' || 
                        (currentUser.permissions && currentUser.permissions.includes(room.id.toString()));
    
    if (!hasPermission) {
        alert('您没有查看此房间详情的权限');
        return;
    }
    
    document.getElementById('roomTitle').textContent = `房间 ${room.roomNumber} 详情`;
    
    const detailsContainer = document.getElementById('roomDetails');
    detailsContainer.innerHTML = generateRoomDetailsHTML(room);
    
    roomModal.style.display = 'block';
}

// 生成房间详情HTML
function generateRoomDetailsHTML(room) {
    const statusText = {
        'empty': '空房',
        'occupied': '正常入住',
        'warning': '租期临近',
        'overdue': '超期未缴费',
        'self-use': '自用房间'
    };
    
    const financialStatusText = {
        'none': '无',
        'paid': '已续费',
        'invoice-pending': '续费后发票未开',
        'invoice-issued': '续费后发票已开'
    };
    
    return `
        <div class="form-group">
            <label>房间状态:</label>
            <span class="status-badge ${room.status}">${statusText[room.status] || room.status}</span>
        </div>
        <div class="form-group">
            <label>入住人:</label>
            <span>${room.tenant || '无'}</span>
        </div>
        <div class="form-group">
            <label>所在单位:</label>
            <span>${room.tenantUnit || '无'}</span>
        </div>
        <div class="form-group">
            <label>联系电话:</label>
            <span>${room.tenantPhone || '无'}</span>
        </div>
        <div class="form-group">
            <label>房间面积:</label>
            <span>${room.area} 平米</span>
        </div>
        <div class="form-group">
            <label>租期开始:</label>
            <span>${room.leaseStartDate ? new Date(room.leaseStartDate).toLocaleDateString() : '无'}</span>
        </div>
        <div class="form-group">
            <label>租期结束:</label>
            <span>${room.leaseEndDate ? new Date(room.leaseEndDate).toLocaleDateString() : '无'}</span>
        </div>
        <div class="form-group">
            <label>财务状况:</label>
            <span>${financialStatusText[room.financialStatus] || room.financialStatus}</span>
        </div>
        <div class="form-group">
            <button class="btn btn-primary" onclick="editRoom(${room.id})">编辑房间信息</button>
            ${room.status === 'warning' || room.status === 'overdue' ?
                `<button class="btn btn-primary" onclick="renewLease(${room.id})">一键续住</button>` : ''}
            <button class="btn btn-secondary" onclick="showMaintenanceRecords(${room.id})">查看维修记录</button>
        </div>
    `;
}

// 编辑房间信息
function editRoom(roomId) {
    const room = rooms.find(r => r.id === roomId);
    if (!room) return;

    const editForm = `
        <form id="editRoomForm">
            <div class="form-group">
                <label for="tenant">入住人:</label>
                <input type="text" id="tenant" name="tenant" value="${room.tenant || ''}">
            </div>
            <div class="form-group">
                <label for="tenantUnit">所在单位:</label>
                <input type="text" id="tenantUnit" name="tenantUnit" value="${room.tenantUnit || ''}">
            </div>
            <div class="form-group">
                <label for="tenantPhone">联系电话:</label>
                <input type="text" id="tenantPhone" name="tenantPhone" value="${room.tenantPhone || ''}">
            </div>
            ${currentUser.role === 'admin' ? `
            <div class="form-group">
                <label for="area">房间面积 (平米):</label>
                <input type="number" id="area" name="area" value="${room.area || 20}">
            </div>
            ` : ''}
            <div class="form-group">
                <label for="leaseStartDate">租期开始:</label>
                <input type="date" id="leaseStartDate" name="leaseStartDate"
                       value="${room.leaseStartDate ? new Date(room.leaseStartDate).toISOString().split('T')[0] : ''}">
            </div>
            <div class="form-group">
                <label for="leaseEndDate">租期结束:</label>
                <input type="date" id="leaseEndDate" name="leaseEndDate"
                       value="${room.leaseEndDate ? new Date(room.leaseEndDate).toISOString().split('T')[0] : ''}">
            </div>
            <div class="form-group">
                <label for="financialStatus">财务状况:</label>
                <select id="financialStatus" name="financialStatus">
                    <option value="none" ${room.financialStatus === 'none' ? 'selected' : ''}>无</option>
                    <option value="paid" ${room.financialStatus === 'paid' ? 'selected' : ''}>已续费</option>
                    <option value="invoice-pending" ${room.financialStatus === 'invoice-pending' ? 'selected' : ''}>续费后发票未开</option>
                    <option value="invoice-issued" ${room.financialStatus === 'invoice-issued' ? 'selected' : ''}>续费后发票已开</option>
                </select>
            </div>
            <div class="form-group">
                <label for="status">房间状态:</label>
                <select id="status" name="status">
                    <option value="empty" ${room.status === 'empty' ? 'selected' : ''}>空房</option>
                    <option value="occupied" ${room.status === 'occupied' ? 'selected' : ''}>正常入住</option>
                    <option value="warning" ${room.status === 'warning' ? 'selected' : ''}>租期临近</option>
                    <option value="overdue" ${room.status === 'overdue' ? 'selected' : ''}>超期未缴费</option>
                    <option value="self-use" ${room.status === 'self-use' ? 'selected' : ''}>自用房间</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="showRoomDetails(${JSON.stringify(room).replace(/"/g, '&quot;')})">取消</button>
            </div>
        </form>
    `;

    document.getElementById('roomDetails').innerHTML = editForm;

    document.getElementById('editRoomForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveRoomChanges(roomId, new FormData(e.target));
    });
}

// 保存房间修改
async function saveRoomChanges(roomId, formData) {
    const updateData = {};
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            updateData[key] = value;
        }
    }

    try {
        const response = await fetch(`/api/rooms/${roomId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        const data = await response.json();

        if (response.ok) {
            alert('房间信息更新成功!');
            await loadRooms(); // 重新加载房间数据
            showRoomDetails(data.room); // 显示更新后的房间详情
        } else {
            alert(data.error || '更新失败');
        }
    } catch (error) {
        console.error('更新房间信息失败:', error);
        alert('更新失败，请稍后重试');
    }
}

// 一键续住
async function renewLease(roomId) {
    const months = prompt('请输入续租月数 (默认12个月):', '12');
    if (!months) return;

    try {
        const response = await fetch(`/api/rooms/${roomId}/renew`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ months: parseInt(months) })
        });

        const data = await response.json();

        if (response.ok) {
            alert(data.message);
            await loadRooms(); // 重新加载房间数据
            showRoomDetails(data.room); // 显示更新后的房间详情
        } else {
            alert(data.error || '续租失败');
        }
    } catch (error) {
        console.error('续租失败:', error);
        alert('续租失败，请稍后重试');
    }
}

// 显示维修记录
async function showMaintenanceRecords(roomId) {
    try {
        const response = await fetch(`/api/rooms/${roomId}/maintenance`);
        if (response.ok) {
            const records = await response.json();
            displayMaintenanceRecords(roomId, records);
        }
    } catch (error) {
        console.error('获取维修记录失败:', error);
    }
}

// 显示维修记录界面
function displayMaintenanceRecords(roomId, records) {
    const recordsHTML = records.map(record => `
        <div class="maintenance-record">
            <h4>维修记录 #${record.id}</h4>
            <p><strong>描述:</strong> ${record.description}</p>
            <p><strong>报告人:</strong> ${record.reportedBy}</p>
            <p><strong>报告时间:</strong> ${new Date(record.reportedAt).toLocaleString()}</p>
            <p><strong>状态:</strong> ${record.status === 'pending' ? '待处理' : '已完成'}</p>
            ${record.completedAt ? `<p><strong>完成时间:</strong> ${new Date(record.completedAt).toLocaleString()}</p>` : ''}
            <p><strong>备注:</strong> ${record.notes || '无'}</p>
        </div>
    `).join('');

    const maintenanceHTML = `
        <div class="maintenance-section">
            <h3>维修记录</h3>
            ${recordsHTML}
            <div class="form-group">
                <button class="btn btn-primary" onclick="addMaintenanceRecord(${roomId})">添加维修记录</button>
            </div>
        </div>
    `;

    document.getElementById('roomDetails').innerHTML += maintenanceHTML;
}

// 添加维修记录
function addMaintenanceRecord(roomId) {
    const description = prompt('请输入维修描述:');
    if (!description) return;

    const notes = prompt('备注 (可选):') || '';

    fetch(`/api/rooms/${roomId}/maintenance`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ description, notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            showMaintenanceRecords(roomId); // 重新显示维修记录
        } else {
            alert(data.error || '添加失败');
        }
    })
    .catch(error => {
        console.error('添加维修记录失败:', error);
        alert('添加失败，请稍后重试');
    });
}
