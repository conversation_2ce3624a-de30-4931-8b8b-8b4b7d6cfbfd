* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.header h1 {
    color: #2c3e50;
    font-size: 28px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-box {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.color-box.green { background-color: #27ae60; }
.color-box.yellow { background-color: #f1c40f; }
.color-box.red { background-color: #e74c3c; }
.color-box.white { background-color: #ffffff; }
.color-box.blue { background-color: #3498db; }

.floors-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.floor {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.floor h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
}

.rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
}

.room {
    width: 80px;
    height: 60px;
    border: 2px solid #ddd;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.room:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.room.empty { background-color: #ffffff; }
.room.occupied { background-color: #27ae60; color: white; }
.room.warning { background-color: #f1c40f; }
.room.overdue { background-color: #e74c3c; color: white; }
.room.self-use { background-color: #3498db; color: white; }

.room-number {
    font-weight: bold;
    font-size: 14px;
}

.room-info {
    font-size: 10px;
    text-align: center;
    margin-top: 2px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
    position: relative;
}

.room-modal {
    width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 10px;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    height: 80px;
    resize: vertical;
}

/* 维修记录样式 */
.maintenance-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.maintenance-record {
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.maintenance-record h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.maintenance-record p {
    margin-bottom: 5px;
    font-size: 14px;
}

/* 状态徽章 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.empty { background-color: #ffffff; border: 1px solid #ddd; }
.status-badge.occupied { background-color: #27ae60; color: white; }
.status-badge.warning { background-color: #f1c40f; }
.status-badge.overdue { background-color: #e74c3c; color: white; }
.status-badge.self-use { background-color: #3498db; color: white; }

/* 表单增强 */
.form-group button {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
    }

    .legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .rooms-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }

    .room {
        width: 60px;
        height: 45px;
    }

    .room-number {
        font-size: 12px;
    }

    .room-info {
        font-size: 8px;
    }

    .maintenance-record {
        padding: 10px;
    }

    .form-group button {
        width: 100%;
        margin-bottom: 10px;
    }
}
