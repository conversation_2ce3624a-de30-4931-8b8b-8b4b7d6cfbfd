我是某一家公司的行政，公司有88个宿舍，我希望做一个网页，可视化管理这些宿舍。需要管理的内容包括：
1、所有的宿舍都会被展现在网页上。房间分布在4层，第一层的房间编号是301-325；第二层的房间编号是401-421，第三层501-521，第四层601-621.
2、所有的宿舍都会用黄色、红色、绿色、白色、蓝色来进行管理，绿色表示正常入住；距离租期只有15天后，会变成黄色，超过租期任然未缴纳房租的，会变成红色，白色是指无人员入住的，蓝色是自用房间。
3、有一键续住的功能，租期临近后，管理人员看到黄色或者红色，就需要及时和租赁人员进行续租。
4、有一个管理员的账号，管理员登录后，可以对所有的信息进行修改，包括续租等等。
5、管理员可以设置子账号，子账号只可以修改入住人信息，并且只能管理指定宿舍的入住人信息，比如只能管理401,501,601的权限。这个权限是可以由管理员设定的。
6、房间有专门一栏财务状况，包括了已续费、续费后发票未开、续费后发票已开等等。
7、每一个房间增加维修记录，可以由管理员子账号进行添加。
8、在房间详情中，增加面积信息，这个面积信息只能由管理员进行编辑，子账号不可编辑。
9、在房间详情中，增加住户所在单位，入住人电话。
10、在未登录的情况下，宿舍管理系统首页，房间只显示房间号和对应色块来区分入住状态。
11、在登录状态下，登录人在宿舍管理系统首页，就可以直观看到所管辖权限的房间的基本信息，不但包括了房间号和色块，还包括入住人单位和入住人信息，点击房间后，可看到更详细的信息。