<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宿舍管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>宿舍管理系统</h1>
            <div class="user-info">
                <span id="userInfo"></span>
                <a id="adminLink" href="/admin.html" class="btn btn-secondary" style="display: none;">管理面板</a>
                <button id="loginBtn" class="btn btn-primary">登录</button>
                <button id="logoutBtn" class="btn btn-secondary" style="display: none;">退出</button>
            </div>
        </header>

        <div class="legend">
            <div class="legend-item">
                <div class="color-box green"></div>
                <span>正常入住</span>
            </div>
            <div class="legend-item">
                <div class="color-box yellow"></div>
                <span>租期临近(15天内)</span>
            </div>
            <div class="legend-item">
                <div class="color-box red"></div>
                <span>超期未缴费</span>
            </div>
            <div class="legend-item">
                <div class="color-box white"></div>
                <span>空房</span>
            </div>
            <div class="legend-item">
                <div class="color-box blue"></div>
                <span>自用房间</span>
            </div>
        </div>

        <div class="floors-container">
            <div class="floor" data-floor="6">
                <h3>6楼 (601-621)</h3>
                <div class="rooms-grid" id="floor6"></div>
            </div>
            <div class="floor" data-floor="5">
                <h3>5楼 (501-521)</h3>
                <div class="rooms-grid" id="floor5"></div>
            </div>
            <div class="floor" data-floor="4">
                <h3>4楼 (401-421)</h3>
                <div class="rooms-grid" id="floor4"></div>
            </div>
            <div class="floor" data-floor="3">
                <h3>3楼 (301-325)</h3>
                <div class="rooms-grid" id="floor3"></div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>用户登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">登录</button>
            </form>
        </div>
    </div>

    <!-- 房间详情模态框 -->
    <div id="roomModal" class="modal">
        <div class="modal-content room-modal">
            <span class="close">&times;</span>
            <h2 id="roomTitle">房间详情</h2>
            <div id="roomDetails"></div>
        </div>
    </div>

    <!-- 报修模态框 -->
    <div id="repairModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>房间报修</h2>
            <form id="repairForm">
                <div class="form-group">
                    <label for="repairType">报修类型:</label>
                    <select id="repairType" name="repairType" required onchange="toggleCustomType()">
                        <option value="">请选择报修类型</option>
                        <option value="water">水路问题</option>
                        <option value="electricity">电路问题</option>
                        <option value="lighting">照明问题</option>
                        <option value="network">网络问题</option>
                        <option value="furniture">家具问题</option>
                        <option value="appliance">电器问题</option>
                        <option value="other">其他问题</option>
                    </select>
                </div>
                <div class="form-group" id="customTypeGroup" style="display: none;">
                    <label for="customType">自定义类型:</label>
                    <input type="text" id="customType" name="customType" placeholder="请输入具体问题类型">
                </div>
                <div class="form-group">
                    <label for="repairDescription">问题描述:</label>
                    <textarea id="repairDescription" name="description" required placeholder="请详细描述遇到的问题..."></textarea>
                </div>
                <div class="form-group">
                    <label for="urgencyLevel">紧急程度:</label>
                    <select id="urgencyLevel" name="urgencyLevel" required>
                        <option value="low">一般</option>
                        <option value="medium">较急</option>
                        <option value="high">紧急</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="contactPhone">联系电话:</label>
                    <input type="tel" id="contactPhone" name="contactPhone" placeholder="方便联系的电话号码">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">提交报修</button>
                    <button type="button" class="btn btn-secondary" onclick="closeRepairModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/js/app.js"></script>
</body>
</html>
