// 全局变量
let currentUser = null;
let users = [];
let rooms = [];
let maintenanceRecords = [];

// DOM 元素
const logoutBtn = document.getElementById('logoutBtn');
const userInfo = document.getElementById('userInfo');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    setupEventListeners();
    loadData();
});

// 设置事件监听器
function setupEventListeners() {
    logoutBtn.addEventListener('click', logout);
    
    // 模态框关闭事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    // 创建用户表单
    document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
}

// 检查认证状态
async function checkAuthStatus() {
    try {
        const response = await fetch('/api/auth/status');
        if (response.ok) {
            const data = await response.json();
            currentUser = data.user;
            
            if (currentUser.role !== 'admin') {
                alert('只有管理员可以访问此页面');
                window.location.href = '/';
                return;
            }
            
            updateUI();
        } else {
            alert('请先登录');
            window.location.href = '/';
        }
    } catch (error) {
        console.error('检查认证状态失败:', error);
        window.location.href = '/';
    }
}

// 更新UI
function updateUI() {
    if (currentUser) {
        userInfo.textContent = `欢迎, ${currentUser.username} (管理员)`;
    }
}

// 退出登录
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST'
        });
        
        if (response.ok) {
            window.location.href = '/';
        }
    } catch (error) {
        console.error('退出登录失败:', error);
    }
}

// 标签页切换
function showTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签按钮的活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 显示选中的标签页
    document.getElementById(`${tabName}-tab`).classList.add('active');
    event.target.classList.add('active');
    
    // 根据标签页加载相应数据
    switch(tabName) {
        case 'users':
            loadUsers();
            break;
        case 'rooms':
            loadRooms();
            break;
        case 'maintenance':
            loadMaintenance();
            break;
    }
}

// 加载所有数据
async function loadData() {
    await Promise.all([
        loadUsers(),
        loadRooms(),
        loadMaintenance()
    ]);
}

// 加载用户数据
async function loadUsers() {
    try {
        const response = await fetch('/api/users');
        if (response.ok) {
            users = await response.json();
            renderUsersTable();
        }
    } catch (error) {
        console.error('加载用户数据失败:', error);
    }
}

// 渲染用户表格
function renderUsersTable() {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.role === 'admin' ? '管理员' : '子账号'}</td>
            <td>${user.role === 'admin' ? '全部权限' : (Array.isArray(user.permissions) ? user.permissions.join(', ') : user.permissions)}</td>
            <td>${new Date(user.createdAt).toLocaleDateString()}</td>
            <td class="action-buttons">
                <button class="btn-small btn-edit" onclick="editUser(${user.id})">编辑</button>
                ${user.id !== 1 ? `<button class="btn-small btn-delete" onclick="deleteUser(${user.id})">删除</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载房间数据
async function loadRooms() {
    try {
        const response = await fetch('/api/rooms');
        if (response.ok) {
            rooms = await response.json();
            renderRoomsTable();
        }
    } catch (error) {
        console.error('加载房间数据失败:', error);
    }
}

// 渲染房间表格
function renderRoomsTable() {
    const tbody = document.getElementById('roomsTableBody');
    tbody.innerHTML = '';
    
    const statusText = {
        'empty': '空房',
        'occupied': '正常入住',
        'warning': '租期临近',
        'overdue': '超期未缴费',
        'self-use': '自用房间'
    };
    
    const financialStatusText = {
        'none': '无',
        'paid': '已续费',
        'invoice-pending': '发票未开',
        'invoice-issued': '发票已开'
    };
    
    rooms.forEach(room => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${room.roomNumber}</td>
            <td>${room.floor}楼</td>
            <td><span class="status-cell ${room.status}">${statusText[room.status] || room.status}</span></td>
            <td>${room.tenant || '-'}</td>
            <td>${room.tenantUnit || '-'}</td>
            <td>${room.tenantPhone || '-'}</td>
            <td>${room.leaseEndDate ? new Date(room.leaseEndDate).toLocaleDateString() : '-'}</td>
            <td>${financialStatusText[room.financialStatus] || room.financialStatus}</td>
            <td class="action-buttons">
                <button class="btn-small btn-edit" onclick="editRoomInTable(${room.id})">编辑</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载维修记录
async function loadMaintenance() {
    try {
        const response = await fetch('/api/maintenance');
        if (response.ok) {
            maintenanceRecords = await response.json();
            renderMaintenanceTable();
        }
    } catch (error) {
        console.error('加载维修记录失败:', error);
    }
}

// 渲染维修记录表格
function renderMaintenanceTable() {
    const tbody = document.getElementById('maintenanceTableBody');
    tbody.innerHTML = '';

    // 按报告时间倒序排列，优先显示报修记录
    const sortedRecords = [...maintenanceRecords].sort((a, b) => {
        // 报修记录优先
        if (a.isRepair && !b.isRepair) return -1;
        if (!a.isRepair && b.isRepair) return 1;

        // 按紧急程度排序
        const urgencyOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        const aUrgency = urgencyOrder[a.urgencyLevel] || 0;
        const bUrgency = urgencyOrder[b.urgencyLevel] || 0;
        if (aUrgency !== bUrgency) return bUrgency - aUrgency;

        // 按时间倒序
        return new Date(b.reportedAt) - new Date(a.reportedAt);
    });

    sortedRecords.forEach(record => {
        const row = document.createElement('tr');

        // 获取类型显示
        const getTypeDisplay = (record) => {
            if (!record.type) return '-';

            const typeNames = {
                'water': '水路',
                'electricity': '电路',
                'lighting': '照明',
                'network': '网络',
                'furniture': '家具',
                'appliance': '电器'
            };

            const typeName = typeNames[record.type] || record.type;
            return `<span class="repair-type ${record.type}">${typeName}</span>`;
        };

        // 获取紧急程度显示
        const getUrgencyDisplay = (record) => {
            if (!record.urgencyLevel) return '-';

            const urgencyText = {
                'low': '一般',
                'medium': '较急',
                'high': '紧急'
            };

            return `<span class="status-cell urgency-${record.urgencyLevel}">${urgencyText[record.urgencyLevel]}</span>`;
        };

        // 处理描述显示（截取前50个字符）
        const shortDescription = record.description.length > 50 ?
            record.description.substring(0, 50) + '...' : record.description;

        row.innerHTML = `
            <td>${record.id}</td>
            <td>${record.roomId}</td>
            <td>${getTypeDisplay(record)}</td>
            <td>${getUrgencyDisplay(record)}</td>
            <td title="${record.description}">${shortDescription}</td>
            <td>${record.reportedBy}</td>
            <td>${record.contactPhone || '-'}</td>
            <td>${new Date(record.reportedAt).toLocaleString()}</td>
            <td><span class="status-cell ${record.status}">${record.status === 'pending' ? '待处理' : '已完成'}</span></td>
            <td class="action-buttons">
                ${record.status === 'pending' ?
                    `<button class="btn-small btn-complete" onclick="completeMaintenance(${record.id})">完成</button>
                     <button class="btn-small btn-edit" onclick="viewMaintenanceDetail(${record.id})">详情</button>` :
                    `<button class="btn-small btn-edit" onclick="viewMaintenanceDetail(${record.id})">详情</button>`}
            </td>
        `;

        // 如果是紧急报修，高亮显示
        if (record.urgencyLevel === 'high' && record.status === 'pending') {
            row.style.backgroundColor = '#fdf2f2';
            row.style.borderLeft = '4px solid #e74c3c';
        }

        tbody.appendChild(row);
    });
}

// 显示创建用户模态框
function showCreateUserModal() {
    generatePermissionsGrid();
    document.getElementById('createUserModal').style.display = 'block';
}

// 生成权限网格
function generatePermissionsGrid() {
    const grid = document.getElementById('permissionsGrid');
    grid.innerHTML = '';
    
    // 生成所有房间的权限复选框
    const floors = [
        { floor: 3, start: 301, end: 325 },
        { floor: 4, start: 401, end: 421 },
        { floor: 5, start: 501, end: 521 },
        { floor: 6, start: 601, end: 621 }
    ];
    
    floors.forEach(floorInfo => {
        for (let roomNum = floorInfo.start; roomNum <= floorInfo.end; roomNum++) {
            const item = document.createElement('div');
            item.className = 'permission-item';
            item.innerHTML = `
                <input type="checkbox" id="room_${roomNum}" value="${roomNum}">
                <label for="room_${roomNum}">${roomNum}</label>
            `;
            grid.appendChild(item);
        }
    });
}

// 切换权限显示
function togglePermissions() {
    const role = document.getElementById('newRole').value;
    const permissionsGroup = document.getElementById('permissionsGroup');
    
    if (role === 'admin') {
        permissionsGroup.style.display = 'none';
    } else {
        permissionsGroup.style.display = 'block';
    }
}

// 处理创建用户
async function handleCreateUser(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const userData = {
        username: formData.get('username'),
        password: formData.get('password'),
        role: formData.get('role')
    };
    
    // 如果是子账号，收集权限
    if (userData.role === 'sub') {
        const permissions = [];
        document.querySelectorAll('#permissionsGrid input[type="checkbox"]:checked').forEach(checkbox => {
            permissions.push(checkbox.value);
        });
        userData.permissions = permissions;
    }
    
    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('用户创建成功!');
            closeModal('createUserModal');
            document.getElementById('createUserForm').reset();
            loadUsers();
        } else {
            alert(data.error || '创建失败');
        }
    } catch (error) {
        console.error('创建用户失败:', error);
        alert('创建失败，请稍后重试');
    }
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 编辑用户 (占位函数)
function editUser(userId) {
    alert('编辑用户功能将在后续实现');
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除此用户吗？')) return;
    
    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('用户删除成功!');
            loadUsers();
        } else {
            alert(data.error || '删除失败');
        }
    } catch (error) {
        console.error('删除用户失败:', error);
        alert('删除失败，请稍后重试');
    }
}

// 在表格中编辑房间 (占位函数)
function editRoomInTable(roomId) {
    alert('房间编辑功能请在主页面进行');
}

// 完成维修
async function completeMaintenance(recordId) {
    const notes = prompt('请输入完成备注:') || '';
    
    try {
        const response = await fetch(`/api/maintenance/${recordId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                status: 'completed',
                notes: notes
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('维修记录已标记为完成!');
            loadMaintenance();
        } else {
            alert(data.error || '更新失败');
        }
    } catch (error) {
        console.error('更新维修记录失败:', error);
        alert('更新失败，请稍后重试');
    }
}

// 过滤房间 (占位函数)
function filterRooms() {
    // 实现房间过滤逻辑
}

// 查看维修详情
function viewMaintenanceDetail(recordId) {
    const record = maintenanceRecords.find(r => r.id === recordId);
    if (!record) return;

    const urgencyText = {
        'low': '一般',
        'medium': '较急',
        'high': '紧急'
    };

    const typeNames = {
        'water': '水路问题',
        'electricity': '电路问题',
        'lighting': '照明问题',
        'network': '网络问题',
        'furniture': '家具问题',
        'appliance': '电器问题'
    };

    const typeName = typeNames[record.type] || record.type || '未分类';

    const detailHTML = `
        <div class="maintenance-detail">
            <h3>维修记录详情 #${record.id}</h3>
            <div class="detail-grid">
                <div class="detail-item">
                    <label>房间号:</label>
                    <span>${record.roomId}</span>
                </div>
                <div class="detail-item">
                    <label>问题类型:</label>
                    <span>${typeName}</span>
                </div>
                <div class="detail-item">
                    <label>紧急程度:</label>
                    <span class="status-cell urgency-${record.urgencyLevel || 'low'}">${urgencyText[record.urgencyLevel] || '一般'}</span>
                </div>
                <div class="detail-item">
                    <label>报告人:</label>
                    <span>${record.reportedBy}</span>
                </div>
                <div class="detail-item">
                    <label>联系电话:</label>
                    <span>${record.contactPhone || '未提供'}</span>
                </div>
                <div class="detail-item">
                    <label>报告时间:</label>
                    <span>${new Date(record.reportedAt).toLocaleString()}</span>
                </div>
                <div class="detail-item">
                    <label>状态:</label>
                    <span class="status-cell ${record.status}">${record.status === 'pending' ? '待处理' : '已完成'}</span>
                </div>
                ${record.completedAt ? `
                <div class="detail-item">
                    <label>完成时间:</label>
                    <span>${new Date(record.completedAt).toLocaleString()}</span>
                </div>
                ` : ''}
                <div class="detail-item full-width">
                    <label>问题描述:</label>
                    <div class="description-box">${record.description}</div>
                </div>
                ${record.notes ? `
                <div class="detail-item full-width">
                    <label>处理备注:</label>
                    <div class="description-box">${record.notes}</div>
                </div>
                ` : ''}
            </div>
            <div class="detail-actions">
                ${record.status === 'pending' ?
                    `<button class="btn btn-primary" onclick="completeMaintenance(${record.id}); closeMaintenanceDetail();">标记完成</button>` :
                    ''}
                <button class="btn btn-secondary" onclick="closeMaintenanceDetail()">关闭</button>
            </div>
        </div>
    `;

    // 创建或更新详情模态框
    let modal = document.getElementById('maintenanceDetailModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'maintenanceDetailModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content maintenance-detail-modal">
                <span class="close" onclick="closeMaintenanceDetail()">&times;</span>
                <div id="maintenanceDetailContent"></div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    document.getElementById('maintenanceDetailContent').innerHTML = detailHTML;
    modal.style.display = 'block';
}

// 关闭维修详情
function closeMaintenanceDetail() {
    const modal = document.getElementById('maintenanceDetailModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 过滤维修记录
function filterMaintenance() {
    const statusFilter = document.getElementById('maintenanceStatusFilter').value;
    const urgencyFilter = document.getElementById('urgencyFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    let filteredRecords = [...maintenanceRecords];

    // 应用状态过滤
    if (statusFilter) {
        filteredRecords = filteredRecords.filter(record => record.status === statusFilter);
    }

    // 应用紧急程度过滤
    if (urgencyFilter) {
        filteredRecords = filteredRecords.filter(record => record.urgencyLevel === urgencyFilter);
    }

    // 应用类型过滤
    if (typeFilter) {
        filteredRecords = filteredRecords.filter(record => record.type === typeFilter);
    }

    // 临时替换数据进行渲染
    const originalRecords = [...maintenanceRecords];
    maintenanceRecords.length = 0;
    maintenanceRecords.push(...filteredRecords);
    renderMaintenanceTable();

    // 恢复原始数据
    maintenanceRecords.length = 0;
    maintenanceRecords.push(...originalRecords);
}
