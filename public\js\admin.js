// 全局变量
let currentUser = null;
let users = [];
let rooms = [];
let maintenanceRecords = [];

// DOM 元素
const logoutBtn = document.getElementById('logoutBtn');
const userInfo = document.getElementById('userInfo');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    setupEventListeners();
    loadData();
});

// 设置事件监听器
function setupEventListeners() {
    logoutBtn.addEventListener('click', logout);
    
    // 模态框关闭事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    // 创建用户表单
    document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
}

// 检查认证状态
async function checkAuthStatus() {
    try {
        const response = await fetch('/api/auth/status');
        if (response.ok) {
            const data = await response.json();
            currentUser = data.user;
            
            if (currentUser.role !== 'admin') {
                alert('只有管理员可以访问此页面');
                window.location.href = '/';
                return;
            }
            
            updateUI();
        } else {
            alert('请先登录');
            window.location.href = '/';
        }
    } catch (error) {
        console.error('检查认证状态失败:', error);
        window.location.href = '/';
    }
}

// 更新UI
function updateUI() {
    if (currentUser) {
        userInfo.textContent = `欢迎, ${currentUser.username} (管理员)`;
    }
}

// 退出登录
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST'
        });
        
        if (response.ok) {
            window.location.href = '/';
        }
    } catch (error) {
        console.error('退出登录失败:', error);
    }
}

// 标签页切换
function showTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签按钮的活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 显示选中的标签页
    document.getElementById(`${tabName}-tab`).classList.add('active');
    event.target.classList.add('active');
    
    // 根据标签页加载相应数据
    switch(tabName) {
        case 'users':
            loadUsers();
            break;
        case 'rooms':
            loadRooms();
            break;
        case 'maintenance':
            loadMaintenance();
            break;
    }
}

// 加载所有数据
async function loadData() {
    await Promise.all([
        loadUsers(),
        loadRooms(),
        loadMaintenance()
    ]);
}

// 加载用户数据
async function loadUsers() {
    try {
        const response = await fetch('/api/users');
        if (response.ok) {
            users = await response.json();
            renderUsersTable();
        }
    } catch (error) {
        console.error('加载用户数据失败:', error);
    }
}

// 渲染用户表格
function renderUsersTable() {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.role === 'admin' ? '管理员' : '子账号'}</td>
            <td>${user.role === 'admin' ? '全部权限' : (Array.isArray(user.permissions) ? user.permissions.join(', ') : user.permissions)}</td>
            <td>${new Date(user.createdAt).toLocaleDateString()}</td>
            <td class="action-buttons">
                <button class="btn-small btn-edit" onclick="editUser(${user.id})">编辑</button>
                ${user.id !== 1 ? `<button class="btn-small btn-delete" onclick="deleteUser(${user.id})">删除</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载房间数据
async function loadRooms() {
    try {
        const response = await fetch('/api/rooms');
        if (response.ok) {
            rooms = await response.json();
            renderRoomsTable();
        }
    } catch (error) {
        console.error('加载房间数据失败:', error);
    }
}

// 渲染房间表格
function renderRoomsTable() {
    const tbody = document.getElementById('roomsTableBody');
    tbody.innerHTML = '';
    
    const statusText = {
        'empty': '空房',
        'occupied': '正常入住',
        'warning': '租期临近',
        'overdue': '超期未缴费',
        'self-use': '自用房间'
    };
    
    const financialStatusText = {
        'none': '无',
        'paid': '已续费',
        'invoice-pending': '发票未开',
        'invoice-issued': '发票已开'
    };
    
    rooms.forEach(room => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${room.roomNumber}</td>
            <td>${room.floor}楼</td>
            <td><span class="status-cell ${room.status}">${statusText[room.status] || room.status}</span></td>
            <td>${room.tenant || '-'}</td>
            <td>${room.tenantUnit || '-'}</td>
            <td>${room.tenantPhone || '-'}</td>
            <td>${room.leaseEndDate ? new Date(room.leaseEndDate).toLocaleDateString() : '-'}</td>
            <td>${financialStatusText[room.financialStatus] || room.financialStatus}</td>
            <td class="action-buttons">
                <button class="btn-small btn-edit" onclick="editRoomInTable(${room.id})">编辑</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载维修记录
async function loadMaintenance() {
    try {
        const response = await fetch('/api/maintenance');
        if (response.ok) {
            maintenanceRecords = await response.json();
            renderMaintenanceTable();
        }
    } catch (error) {
        console.error('加载维修记录失败:', error);
    }
}

// 渲染维修记录表格
function renderMaintenanceTable() {
    const tbody = document.getElementById('maintenanceTableBody');
    tbody.innerHTML = '';
    
    maintenanceRecords.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.id}</td>
            <td>${record.roomId}</td>
            <td>${record.description}</td>
            <td>${record.reportedBy}</td>
            <td>${new Date(record.reportedAt).toLocaleString()}</td>
            <td><span class="status-cell ${record.status}">${record.status === 'pending' ? '待处理' : '已完成'}</span></td>
            <td class="action-buttons">
                ${record.status === 'pending' ? 
                    `<button class="btn-small btn-complete" onclick="completeMaintenance(${record.id})">完成</button>` : 
                    '已完成'}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示创建用户模态框
function showCreateUserModal() {
    generatePermissionsGrid();
    document.getElementById('createUserModal').style.display = 'block';
}

// 生成权限网格
function generatePermissionsGrid() {
    const grid = document.getElementById('permissionsGrid');
    grid.innerHTML = '';
    
    // 生成所有房间的权限复选框
    const floors = [
        { floor: 3, start: 301, end: 325 },
        { floor: 4, start: 401, end: 421 },
        { floor: 5, start: 501, end: 521 },
        { floor: 6, start: 601, end: 621 }
    ];
    
    floors.forEach(floorInfo => {
        for (let roomNum = floorInfo.start; roomNum <= floorInfo.end; roomNum++) {
            const item = document.createElement('div');
            item.className = 'permission-item';
            item.innerHTML = `
                <input type="checkbox" id="room_${roomNum}" value="${roomNum}">
                <label for="room_${roomNum}">${roomNum}</label>
            `;
            grid.appendChild(item);
        }
    });
}

// 切换权限显示
function togglePermissions() {
    const role = document.getElementById('newRole').value;
    const permissionsGroup = document.getElementById('permissionsGroup');
    
    if (role === 'admin') {
        permissionsGroup.style.display = 'none';
    } else {
        permissionsGroup.style.display = 'block';
    }
}

// 处理创建用户
async function handleCreateUser(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const userData = {
        username: formData.get('username'),
        password: formData.get('password'),
        role: formData.get('role')
    };
    
    // 如果是子账号，收集权限
    if (userData.role === 'sub') {
        const permissions = [];
        document.querySelectorAll('#permissionsGrid input[type="checkbox"]:checked').forEach(checkbox => {
            permissions.push(checkbox.value);
        });
        userData.permissions = permissions;
    }
    
    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('用户创建成功!');
            closeModal('createUserModal');
            document.getElementById('createUserForm').reset();
            loadUsers();
        } else {
            alert(data.error || '创建失败');
        }
    } catch (error) {
        console.error('创建用户失败:', error);
        alert('创建失败，请稍后重试');
    }
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 编辑用户 (占位函数)
function editUser(userId) {
    alert('编辑用户功能将在后续实现');
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除此用户吗？')) return;
    
    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('用户删除成功!');
            loadUsers();
        } else {
            alert(data.error || '删除失败');
        }
    } catch (error) {
        console.error('删除用户失败:', error);
        alert('删除失败，请稍后重试');
    }
}

// 在表格中编辑房间 (占位函数)
function editRoomInTable(roomId) {
    alert('房间编辑功能请在主页面进行');
}

// 完成维修
async function completeMaintenance(recordId) {
    const notes = prompt('请输入完成备注:') || '';
    
    try {
        const response = await fetch(`/api/maintenance/${recordId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                status: 'completed',
                notes: notes
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('维修记录已标记为完成!');
            loadMaintenance();
        } else {
            alert(data.error || '更新失败');
        }
    } catch (error) {
        console.error('更新维修记录失败:', error);
        alert('更新失败，请稍后重试');
    }
}

// 过滤房间 (占位函数)
function filterRooms() {
    // 实现房间过滤逻辑
}

// 过滤维修记录 (占位函数)
function filterMaintenance() {
    // 实现维修记录过滤逻辑
}
